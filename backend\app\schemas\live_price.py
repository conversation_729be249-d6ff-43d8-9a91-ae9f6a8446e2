from typing import Dict, List, Optional

from pydantic import BaseModel


class LivePrice(BaseModel):
    """Schema for live price data"""
    trading_code: str
    price: float
    last_updated: str  # ISO format timestamp


class LivePriceResponse(BaseModel):
    """Response schema for live price data"""
    prices: Dict[str, LivePrice]


class StockDetails(BaseModel):
    """Schema for additional stock details"""
    trading_code: str
    opening_price: Optional[float] = None
    week_52_low: Optional[float] = None
    pe_ratio: Optional[float] = None
    last_updated: str  # ISO format timestamp


class StockDetailsResponse(BaseModel):
    """Response schema for stock details data"""
    details: Dict[str, StockDetails]
