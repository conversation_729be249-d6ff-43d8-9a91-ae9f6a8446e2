import logging
import logging.handlers
import os
from datetime import datetime, timedelta
from pathlib import Path


class CleanupTimedRotatingFileHandler(logging.handlers.TimedRotatingFileHandler):
    """Custom handler that automatically deletes old log files"""
    
    def __init__(self, filename, when='midnight', interval=1, backupCount=7, **kwargs):
        super().__init__(filename, when, interval, backupCount, **kwargs)
        self.cleanup_old_files()
    
    def cleanup_old_files(self):
        """Remove log files older than 7 days"""
        try:
            log_dir = Path(self.baseFilename).parent
            if not log_dir.exists():
                return
                
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for log_file in log_dir.glob("*.log*"):
                try:
                    file_time = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        log_file.unlink()
                        print(f"Deleted old log file: {log_file}")
                except Exception as e:
                    print(f"Error deleting log file {log_file}: {e}")
        except Exception as e:
            print(f"Error during log cleanup: {e}")


def setup_logging():
    """Configure logging for the application"""
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Generate filename with current date
    log_filename = logs_dir / f"dse_analyzen_{datetime.now().strftime('%Y%m%d')}.log"
    
    # Create custom formatter for clean console output
    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Create detailed formatter for file output
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Remove any existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler - only show WARNING and above
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler - only show ERROR and above, with daily rotation
    file_handler = CleanupTimedRotatingFileHandler(
        filename=str(log_filename),
        when='midnight',
        interval=1,
        backupCount=7,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.ERROR)
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Silence noisy loggers
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.dialects').setLevel(logging.WARNING)
    logging.getLogger('asyncpg').setLevel(logging.WARNING)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    
    # Create application logger
    app_logger = logging.getLogger('dse_analyzen')
    app_logger.setLevel(logging.INFO)
    
    return app_logger


def get_logger(name: str = 'dse_analyzen') -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)
