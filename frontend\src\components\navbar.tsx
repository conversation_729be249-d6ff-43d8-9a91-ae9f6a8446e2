"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { UserIcon } from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

export default function Navbar() {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [formData, setFormData] = useState({
    fullName: session?.user?.name || "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
    commission: 0.0,
    investmentAmount: 0.0,
  });

  // Track if we've loaded the portfolio data
  const [portfolioDataLoaded, setPortfolioDataLoaded] = useState(false);

  // Update form data when session changes
  useEffect(() => {
    if (session?.user?.name) {
      setFormData((prev) => ({
        ...prev,
        fullName: session.user.name || "",
      }));
    }
  }, [session]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        name === "commission" || name === "investmentAmount"
          ? parseFloat(value) || 0
          : value,
    }));
  };

  const fetchPortfolioData = useCallback(async () => {
    if (!session?.user?.accessToken) return;

    // Skip if we've already loaded the data
    if (portfolioDataLoaded) return;

    try {
      console.log("Fetching portfolio data...");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`,
        {
          headers: {
            Authorization: `Bearer ${session.user.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        console.log("Portfolio data received:", data);
        setFormData((prev) => ({
          ...prev,
          commission: data.commission || 0.0,
          investmentAmount: data.investment_amount || 0.0,
        }));
        setPortfolioDataLoaded(true);
      } else {
        console.error("Failed to fetch portfolio data:", await response.text());
      }
    } catch (error: unknown) {
      console.error("Error fetching portfolio data:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load portfolio settings",
      });
    }
  }, [session, portfolioDataLoaded, toast]);

  // Fetch portfolio data when session is available
  useEffect(() => {
    if (session?.user?.accessToken && isProfileOpen) {
      fetchPortfolioData();
    }
  }, [session, isProfileOpen, fetchPortfolioData]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      formData.newPassword &&
      formData.newPassword !== formData.confirmPassword
    ) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "New passwords do not match",
      });
      return;
    }

    try {
      // Update user profile
      const userResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/me`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            full_name: formData.fullName,
            ...(formData.newPassword && formData.currentPassword
              ? {
                  password: formData.newPassword,
                  current_password: formData.currentPassword,
                }
              : {}),
          }),
        }
      );

      if (!userResponse.ok) {
        const error = await userResponse.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to update profile",
        });
        return;
      }

      // Update portfolio settings
      const portfolioResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
          body: JSON.stringify({
            commission: formData.commission,
            investment_amount: formData.investmentAmount,
          }),
        }
      );

      if (!portfolioResponse.ok) {
        const error = await portfolioResponse.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to update portfolio settings",
        });
        return;
      }

      toast({
        variant: "success",
        title: "Success",
        description: "Profile updated successfully",
      });
      setIsProfileOpen(false);
      // Refresh the session to get updated user data
      router.refresh();
    } catch (error: unknown) {
      console.error("Profile update error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while updating profile",
      });
    }
  };

  const handleSyncTradingCodes = async () => {
    if (isSyncing) return;

    setIsSyncing(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/admin/sync-trading-codes`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session?.user?.accessToken}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        toast({
          variant: "success",
          title: "Success",
          description: `Trading codes synced successfully. Updated ${data.codes_updated} codes.`,
        });
      } else {
        const error = await response.json();
        toast({
          variant: "destructive",
          title: "Error",
          description: error.detail || "Failed to sync trading codes",
        });
      }
    } catch (error: unknown) {
      console.error("Sync trading codes error:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while syncing trading codes",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/login" });
  };

  return (
    <nav className="bg-white border-b border-gray-200 px-4 py-2.5 fixed w-full top-0 left-0 z-10">
      <div className="flex flex-wrap justify-between items-center">
        <div className="flex items-center">
          <span className="self-center text-xl font-semibold whitespace-nowrap">
            DSE Analyzen
          </span>
        </div>

        {session?.user && (
          <div className="flex items-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                >
                  <UserIcon className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setIsProfileOpen(true)}>
                  Profile Settings
                </DropdownMenuItem>
                {session.user.isAdmin && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={handleSyncTradingCodes}
                      disabled={isSyncing}
                    >
                      {isSyncing ? "Syncing..." : "Sync Trading Codes"}
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      <Dialog
        open={isProfileOpen}
        onOpenChange={(open) => {
          setIsProfileOpen(open);
          if (open) {
            // Reset the loaded state and fetch fresh data when dialog opens
            setPortfolioDataLoaded(false);
            fetchPortfolioData();
          }
        }}
      >
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Profile Settings</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleProfileUpdate} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column - User Profile */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">User Profile</h3>
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              {/* Right Column - Portfolio Preferences */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Portfolio Preferences</h3>
                <div className="space-y-2">
                  <Label htmlFor="commission">
                    Broker House Commission (%)
                  </Label>
                  <Input
                    id="commission"
                    name="commission"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    value={formData.commission}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-muted-foreground">
                    This percentage will be used to calculate the final price
                    per unit as: Buy Price × (1 + commission%)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="investmentAmount">
                    Investment Amount (BDT)
                  </Label>
                  <Input
                    id="investmentAmount"
                    name="investmentAmount"
                    type="number"
                    step="1000"
                    min="0"
                    value={formData.investmentAmount}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-muted-foreground">
                    Your total investment amount for portfolio management
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsProfileOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </nav>
  );
}
