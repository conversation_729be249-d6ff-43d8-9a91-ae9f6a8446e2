import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useToast } from "./use-toast";

export interface LivePrice {
  trading_code: string;
  price: number;
  last_updated: string;
}

export function useLivePrices() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [livePrices, setLivePrices] = useState<Record<string, LivePrice>>(
    () => {
      if (typeof window !== "undefined") {
        try {
          const cachedData = localStorage.getItem("livePrices");
          const cachedTimestamp = localStorage.getItem("livePricesTimestamp");

          if (cachedData && cachedTimestamp) {
            const timestamp = parseInt(cachedTimestamp, 10);
            const fiveMinutesInMs = 5 * 60 * 1000;
            if (Date.now() - timestamp < fiveMinutesInMs) {
              return JSON.parse(cachedData);
            }
          }
        } catch (error) {
          console.error("Error loading cached prices:", error);
        }
      }
      return {};
    }
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isOnDashboard, setIsOnDashboard] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastFetchTimeRef = useRef<number>(0);
  const marketStatusRef = useRef<boolean | null>(null);
  const lastMarketCheckRef = useRef<number>(0);
  const initialFetchDoneRef = useRef<boolean>(false);
  const tradingCodesRef = useRef<string[]>([]);
  const lastSetupRef = useRef<{ active: boolean; codes: string[] }>({
    active: false,
    codes: [],
  });

  useEffect(() => {
    if (Object.keys(livePrices).length > 0) {
      try {
        localStorage.setItem("livePrices", JSON.stringify(livePrices));
        localStorage.setItem("livePricesTimestamp", Date.now().toString());
      } catch (error) {
        console.error("Error caching live prices:", error);
      }
    }
  }, [livePrices]);

  const isMarketOpen = useCallback((): boolean => {
    const now = Date.now();
    if (
      marketStatusRef.current !== null &&
      now - lastMarketCheckRef.current < 60000
    ) {
      return marketStatusRef.current;
    }

    const currentDate = new Date();
    const bstOffset = 6 * 60;
    const utcMinutes =
      currentDate.getUTCHours() * 60 + currentDate.getUTCMinutes();
    const bstMinutes = (utcMinutes + bstOffset) % (24 * 60);
    const bstHour = Math.floor(bstMinutes / 60);
    const bstDay = currentDate.getUTCDay();

    const isMarketHours =
      bstHour >= 10 &&
      (bstHour < 14 || (bstHour === 14 && bstMinutes % 60 <= 30));

    const isWeekend = bstDay === 5 || bstDay === 6;
    const isOpen = isMarketHours && !isWeekend;

    marketStatusRef.current = isOpen;
    lastMarketCheckRef.current = now;

    return isOpen;
  }, []);

  const fetchLivePrices = useCallback(
    async (tradingCodes: string[], forceRefresh = false) => {
      if (!session?.user?.accessToken || !tradingCodes.length) return;

      const now = Date.now();
      if (now - lastFetchTimeRef.current < 10000 && !forceRefresh) {
        return;
      }

      const marketOpen = isMarketOpen();
      const isInitialFetch = Object.keys(livePrices).length === 0;

      if (!marketOpen && !isInitialFetch && !forceRefresh) {
        return;
      }

      lastFetchTimeRef.current = now;
      setIsLoading(true);

      try {
        const response = await fetch(
          `${
            process.env.NEXT_PUBLIC_API_URL
          }/live-prices?codes=${tradingCodes.join(",")}`,
          { headers: { Authorization: `Bearer ${session.user.accessToken}` } }
        );

        if (response.ok) {
          const data = await response.json();
          setLivePrices(data.prices || {});
        } else {
          console.error("Failed to fetch live prices:", await response.text());
        }
      } catch (error) {
        console.error("Error fetching live prices:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [session, isMarketOpen, livePrices]
  );

  const refreshPrices = useCallback(
    async (tradingCodes: string[]) => {
      if (!tradingCodes.length) return;
      await fetchLivePrices(tradingCodes, true);
      toast({
        title: "Refreshing Prices",
        description: "Live prices have been updated",
      });
    },
    [fetchLivePrices, toast]
  );

  const startPolling = useCallback(
    (tradingCodes: string[]) => {
      if (!tradingCodes.length) return;

      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }

      if (!initialFetchDoneRef.current) {
        fetchLivePrices(tradingCodes, true);
        initialFetchDoneRef.current = true;
      }

      pollingIntervalRef.current = setInterval(() => {
        if (isMarketOpen()) {
          fetchLivePrices(tradingCodes);
        }
      }, 5 * 60 * 1000);
    },
    [fetchLivePrices, isMarketOpen]
  );

  const setDashboardActive = useCallback(
    (active: boolean, tradingCodes: string[] = []) => {
      const sortedNewCodes = [...tradingCodes].sort();
      const sortedCurrentCodes = [...lastSetupRef.current.codes].sort();
      const codesStr = JSON.stringify(sortedNewCodes);
      const lastCodesStr = JSON.stringify(sortedCurrentCodes);

      if (active === lastSetupRef.current.active && codesStr === lastCodesStr) {
        return;
      }

      lastSetupRef.current = { active, codes: [...tradingCodes] };
      tradingCodesRef.current = [...tradingCodes];

      if (active !== isOnDashboard) {
        setIsOnDashboard(active);
      }

      if (active && tradingCodes.length > 0) {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        startPolling(tradingCodes);
      } else if (!active) {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        initialFetchDoneRef.current = false;
      }
    },
    [startPolling, isOnDashboard]
  );

  return {
    livePrices,
    isLoading,
    setDashboardActive,
    refreshPrices,
    startPolling,
    isMarketOpen,
  };
}
